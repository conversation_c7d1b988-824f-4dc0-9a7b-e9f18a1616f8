<template>
  <div class="survey-detail">
    <div class="content">
      <!-- <div class="page-title">勘查历史记录</div> -->
      <!-- 品规识别记录列表 -->
      <van-empty v-if="identifyList.length === 0" description="暂无识别记录" />

      <div v-for="(item, index) in identifyList" :key="index">
        <div class="title">
          <span class="title-two">{{ item.identifyDate }}</span>
        </div>
        <div class="info-card">
          <!-- 零售户信息 -->
          <div class="retailer-info" v-if="item.retailerName || item.retailerLicNo || item.retailerAddress">
            <div class="retailer-name" v-if="item.retailerName">{{ item.retailerName }}</div>
            <div class="retailer-lic" v-if="item.retailerLicNo">许可证号：{{ item.retailerLicNo }}</div>
            <div class="retailer-address" v-if="item.retailerAddress">
              <span class="address-label">地址：</span>
              <span class="address-content">{{ item.retailerAddress }}</span>
            </div>
            <van-divider />
          </div>

          <div class="flex">
            <span class="text">识别品规数：</span>
            <span class="text">{{ item.identifyNum }}</span>
          </div>
          <div class="flex" style="margin-top: 10px; margin-bottom: 20px">
            <span class="text">识别异常数：</span>
            <span class="text text-time">{{ item.errorNum }}</span>
          </div>
          <van-divider />
          <div class="btn-container">
            <div class="creator-name" v-if="item.creatorRealName">操作人：{{ item.creatorRealName }}</div>
            <div class="pgsb-btn" @click="handleViewClick(item)">查看详情</div>
          </div>
        </div>
      </div>

      <div class="load-more" v-if="hasMore">
        <van-button size="small" type="primary" @click="loadMore" :loading="loading">加载更多</van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { getWeekByDate } from '@/utils/dateUtil'
import { http } from '@/utils/http'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 判断用户是否为管理员
const isAdmin = computed(() => {
  // 获取用户角色信息
  const roleName = userStore.userInfo?.role_name;

  // 如果没有角色信息，则默认为非管理员
  if (!roleName) return false;

  // 定义管理员角色列表
  const adminRoles = ['admin', 'administrator', 'manager'];

  // 将角色字符串拆分为数组（处理可能的多角色情况）
  const userRoles = roleName.split(',');

  // 检查用户的任一角色是否在管理员角色列表中
  return userRoles.some(role => adminRoles.includes(role.trim()));
});

// 页面数据
const identifyList = ref([])
const loading = ref(false)
const hasMore = ref(false)

// 分页相关
const pageInfo = ref({
  current: 1,
  size: 10,
  total: 0
})

// 查看详情
const handleViewClick = (item) => {
  router.push({
    path: '/pgsb-info',
    query: {
      type: 'view',
      date: item.identifyDate,
      licNo: item.licNo || '',
      explorationId: item.explorationId
    }
  })
}

// 加载更多
const loadMore = () => {
  if (loading.value) return

  pageInfo.value.current++
  loadIdentifyList()
}

// 加载品规识别记录列表
const loadIdentifyList = async () => {
  try {
    loading.value = true

    // 创建请求参数对象
    const params = {
      current: pageInfo.value.current,
      size: pageInfo.value.size
    }

    // 只有非管理员才添加createUser参数
    if (!isAdmin.value) {
      params.createUser = userStore.userInfo?.user_id
    }

    const res = await http.get('/api/dingapp/itemIdentifyResults/userRecords', {
      params
    })

    if (res.success && res.data) {
      const { records, total } = res.data

      if (records && records.length > 0) {
        // 格式化数据
        const formattedRecords = records.map(item => ({
          identifyDate: item.identifyDate + ' ' + item.timeSegment + ' ' + getWeekByDate(item.identifyDate),
          identifyNum: item.identifyNum,
          errorNum: item.errorNum,
          explorationId: item.explorationId,
          licNo: item.licNo,
          retailerName: item.retailerName,
          retailerLicNo: item.retailerLicNo,
          retailerAddress: item.retailerAddress,
          creatorRealName: item.creatorRealName || '' // 添加创建者姓名
        }))

        // 如果是第一页，直接赋值；否则追加
        if (pageInfo.value.current === 1) {
          identifyList.value = formattedRecords
        } else {
          identifyList.value = [...identifyList.value, ...formattedRecords]
        }

        // 更新分页信息
        pageInfo.value.total = total

        // 判断是否还有更多数据
        hasMore.value = identifyList.value.length < total
      } else {
        if (pageInfo.value.current === 1) {
          identifyList.value = []
        }
        hasMore.value = false
      }
    } else {
      if (pageInfo.value.current === 1) {
        identifyList.value = []
      }
      hasMore.value = false
      showToast(res.msg || '获取识别记录失败')
    }
  } catch (error) {
    console.error('加载识别记录失败:', error)
    showToast('加载识别记录失败')
    if (pageInfo.value.current === 1) {
      identifyList.value = []
    }
    hasMore.value = false
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 直接加载识别记录列表，不再需要先加载勘查详情
  loadIdentifyList()
})
</script>

<style lang="scss" scoped>
.survey-detail {
  min-height: 100vh;
  background-color: #f7f8fa;

  .content {
    padding:  12px;
  }

  .page-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
    text-align: center;
  }

  .title {
    margin: 10px 0;
    font-size: 14px;

    .title-two {
      padding-left: 10px;
      display: inline-block;
      color: #666;
    }
  }

  .info-card {
    width: 100%;
    background: #fff;
    border-radius: 15px;
    box-sizing: border-box;
    margin: 10px auto 16px;
    padding: 16px 16px 10px;
    min-height: 130px;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, .1);

    .retailer-info {
      margin-bottom: 10px;

      .retailer-name {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
        word-break: break-all;
        line-height: 1.4;
      }

      .retailer-lic {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .retailer-address {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        display: flex;

        .address-label {
          flex-shrink: 0;
          margin-right: 4px;
        }

        .address-content {
          word-break: break-all;
          line-height: 1.4;
        }
      }
    }

    .flex {
      display: flex;
      align-items: flex-start;
    }

    .text {
      font-size: 14px;
      color: #333;
      flex: 1;
      min-width: 0;
      white-space: normal;
      word-break: break-all;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      line-clamp: 3;
      -webkit-box-orient: vertical;

      &.text-time {
        color: #666;
        -webkit-line-clamp: 2;
        line-clamp: 2;
      }
    }

    .btn-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-top: 10px;

      .creator-name {
        font-size: 14px;
        color: #666;
      }
    }

    .pgsb-btn {
      background-color: #1c88f9;
      color: #fff;
      text-align: center;
      border-radius: 7px;
      padding: 6px 15px;
      font-size: 14px;
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }
    }
  }

  .load-more {
    display: flex;
    justify-content: center;
    margin: 20px 0;
  }



  .date-filter {
    margin-bottom: 12px;
  }

  .section-title {
    font-size: 16px;
    font-weight: 500;
    margin: 16px 0 8px 12px;
  }

  .record-card {
    margin-bottom: 12px;
  }

  .image-container {
    display: flex;
    padding: 12px;
    gap: 12px;
    overflow-x: auto;

    .image-wrapper {
      position: relative;
      flex-shrink: 0;

      .identify-image {
        width: 120px;
        height: 120px;
        border-radius: 4px;
      }

      .result-label {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        font-size: 12px;
        padding: 2px 4px;
        text-align: center;
      }
    }
  }

  .advice-container {
    padding: 16px;

    .advice-label {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }

    .advice-content {
      font-size: 14px;
      color: #17a0bb;
      line-height: 1.5;
    }
  }

  .table-container {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    .table-header {
      display: flex;
      background-color: #f7f8fa;
      border-bottom: 1px solid #ebedf0;

      .col-item {
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        white-space: nowrap;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        border-bottom: 1px solid #ebedf0;

        &:last-child {
          border-bottom: none;
        }

        .col-item {
          padding: 12px 16px;
          font-size: 14px;
          color: #646566;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;

          &.collision-type-duplicate {
            color: #ff9800;
          }

          &.collision-type-error {
            color: #f44336;
          }

          &.collision-type-normal {
            color: #4caf50;
          }
        }
      }

      .table-row:nth-child(even) {
        background-color: #fafafa;
      }
    }
  }

  .result-list {
    padding: 0 12px 12px;

    .result-title {
      font-size: 14px;
      font-weight: 500;
      margin: 8px 0;
      color: #323233;
    }

    .result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .result-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .result-count {
        flex-shrink: 0;
        margin-left: 8px;
        color: #1989fa;
      }
    }
  }
}
</style>
