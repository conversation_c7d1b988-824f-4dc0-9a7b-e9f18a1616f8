<template>
  <div class="evidence-container">
    <div class="search-panel">
      <div class="search-box-wrapper">
        <div class="search-box">
          <van-search
            v-model="searchValue"
            placeholder="请输入标题自动搜索"
            class="search-bar"
            shape="round"
            clearable
          >
          </van-search>
        </div>
      </div>

      <div class="add-btn-wrapper">
        <div class="btn-group">
          <van-button type="default" icon="description" @click="showImportDialog" class="import-btn">
            导入
          </van-button>
          <van-button type="primary" icon="plus" @click="toDetail({id: 'new'})" class="add-btn">
            新增专卖快报
          </van-button>
        </div>
      </div>
    </div>

    <!-- 移除了新增涉案物品时的零售户选择弹窗 -->

    <van-empty v-if="itemsList.length === 0" description="暂无专卖快报记录" />
    <van-list v-else v-model:loading="loading" :finished="finished" :finished-text="scrollToast || '没有更多了'" @load="scrollToLowerLoad">
      <div class="items-list">
        <van-swipe-cell v-for="item in itemsList" :key="item.id" class="swipe-cell">
          <div class="item-card">
            <div class="item-content" @click="toDetail(item)" >
              <div class="item-title">{{ item.title|| item.copanyName }}</div>
              <!-- <div class="item-desc">{{ item.itemType }}</div> -->
              <div class="item-info">
                <span class="item-time">{{ item.createTime }}</span>
              </div>
            </div>
            <!-- <div class="item-actions">
              <div class="action-icon download-icon" @click.stop="toDetailTable(item)">
                <van-icon name="photograph" size="20" color="#1989fa" />
              </div>
            </div> -->
          </div>
          <template #right>
            <div class="delete-button" @click="handleDeleteItem(item)">
              <van-icon name="delete-o" size="20" />
              <span>删除</span>
            </div>
          </template>
        </van-swipe-cell>
      </div>
    </van-list>

    <!-- 悬浮添加按钮 -->
    <van-fab
      class="add-button"
      icon="plus"
      color="#1989fa"
      @click="toDetail({id: 'new'})"
    />

    <!-- 已移除零售户选择弹窗 -->

    <!-- 文本导入弹窗 -->
    <van-dialog
      v-model:show="importDialogVisible"
      title="文本导入"
      :show-confirm-button="true"
      :show-cancel-button="true"
      confirm-button-text="确定"
      cancel-button-text="取消"
      @confirm="handleImportConfirm"
      class="import-dialog"
    >
      <div class="import-dialog-content">
        <van-field
          v-model="importText"
          type="textarea"
          placeholder="请将要导入的文本粘贴到这里"
          rows="8"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { http } from '@/utils/http'
import { showToast, showSuccessToast, showFailToast, showConfirmDialog, closeToast } from 'vant'
import * as dd from 'dingtalk-jsapi'

const router = useRouter()
const searchValue = ref('')
const loading = ref(false)
const finished = ref(false)
const scrollToast = ref('')
const itemsList = ref([])
const searchTimeout = ref(null) // 用于防抖

// 文本导入相关状态
const importDialogVisible = ref(false)
const importText = ref('')

const formPage = ref({
  pageNo: 1,
  pageSize: 15,
  total: 0
})

//搜索扫码
const handleSearchBarScan = async() => {
  dd.scan({
        type: 'qr',
        onSuccess: (res) => {
            const text = res.text

            if (!text) {
                showToast('未识别到二维码')
                return
            }

            const licenseNumberMatch = text.match(/许可证号[:：]\s*(\d+)/)
            if (licenseNumberMatch) {
                const scannedLicenseNumber = licenseNumberMatch[1]
                // 先调用接口获取数据，只有在成功获取到数据后才跳转
                searchValue.value = scannedLicenseNumber
                getItemsList()
            } else {
                showToast('未识别许可证号')
            }
        },
        fail: (err) => {
            console.error('扫码失败:', err)
            showToast('扫码失败')
        }
    })
}
const handleSearch = () => {
  formPage.value.pageNo = 1
  itemsList.value = []
  finished.value = false
  getItemsList()
}

const toDetail = (item) => {
  if (item.id === 'new') {
    // 直接跳转到新增页面，不再选择零售户
    router.push({
      path: '/evidence-items/detail',
      query: {
        id: 'new'
      }
    })
    return
  }
  router.push({
    path: '/evidence-items/detail',
    query: {
      selectionTime: item.selectionTime,
      // yhytId: item.yhytId
    }
  })
}

// 跳转到表格形式的详情页面
const toDetailTable = (item) => {
  router.push({
    path: '/evidence-items/detail-table',
    query: {
      selectionTime: item.selectionTime,
      title: item.title
    }
  })
}



const userStore = useUserStore()
// const userId = userStore.userInfo?.user_id
const userId = userStore.userInfo?.user_id || '1887793455687979013'

const getItemsList = async () => {
  try {
    const res = await http.get('/api/dingapp/ecidenceYhyt/list', {
      params: {
        current: formPage.value.pageNo,
        size: formPage.value.pageSize,
        searchText: searchValue.value,
        createUser: userId
      }
    })

    if (res.data) {
      let list = []
      if (formPage.value.pageNo > 1) {
        list = [...itemsList.value]
      }

      const resData = res.data.records || []
      list.push(...resData)

      formPage.value.total = res.data.total || 0
      formPage.value.pageSize = res.data.size || 15
      formPage.value.pageNo = res.data.current || 1
      itemsList.value = list

      loading.value = false
      if (itemsList.value.length >= formPage.value.total) {
        finished.value = true
        if (formPage.value.pageNo === 1 && formPage.value.total < 15) {
          scrollToast.value = ''
        } else {
          scrollToast.value = '已经到底啦'
        }
      }
    }

  } catch (error) {
    console.error('获取涉案物品列表失败:', error)
    loading.value = false
    finished.value = true
  }
}

const scrollToLowerLoad = () => {
  if (formPage.value.pageNo * formPage.value.pageSize >= formPage.value.total) {
    return
  }

  formPage.value.pageNo += 1
  loading.value = true
  getItemsList()
}

// 移除了弹窗关闭处理函数

// 删除涉案物品记录
const handleDeleteItem = async (item) => {
  try {
    // 显示确认对话框
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除${item.title}这条涉案物品记录吗？`,
    })

    // 用户确认删除，调用删除API
    showToast({
      type: 'loading',
      message: '删除中...',
      forbidClick: true,
      duration: 0
    })

    // 调用删除API
    const res = await http.get(`/api/dingapp/ecidenceYhyt/deleteByYhytIdAndTime?selectionTimeStr=${item.selectionTime}&userId=${userId}`)

    closeToast()

    if (res.success) {
      showSuccessToast('删除成功')
      // 重新加载列表
      formPage.value.pageNo = 1
      itemsList.value = []
      finished.value = false
      await getItemsList()
    } else {
      showFailToast(res.msg || '删除失败')
    }
  } catch (error) {
    // 用户取消删除或发生错误
    closeToast()
    if (error.toString().includes('cancel')) {
      // 用户取消操作，不做处理
      return
    }
    showFailToast('删除失败：' + (error.message || '未知错误'))
  }
}

// 导出Excel
const handleExportExcel = async (item) => {

  try {
    // 显示加载提示
    showToast({
      type: 'loading',
      message: '正在获取文件...',
      forbidClick: true,
      duration: 0
    })

    // 调用接口获取文件URL
    const res = await http.get(`/api/dingapp/ecidenceYhyt/exportExcel?selectionTime=${item.selectionTime}`)

    closeToast()

    if (!res.success || !res.data) {
      showFailToast(res.msg || '获取文件地址失败')
      return
    }

    // 从接口返回中获取文件URL
    const minioBaseUrl = import.meta.env.VITE_BASE_MINIO_URI

    // 获取文件路径
    let path = res.data.path || ''

    // 如果是开发环境，需要处理/minio前缀
    if (import.meta.env.MODE === 'development' && path.startsWith('/minio')) {
      path = path.replace('/minio', '')
    }

    // 构建完整的文件URL
    const fileUrl = path.startsWith('http') ? path : `${minioBaseUrl}${path}`
    // const fileUrl = 'http://10-78-81-8-aawey4n5ob9ct3.ztna-dingtalk.com/minio/images/upload/20250421/490247741397c7b1a1a046dd3d9cba81.xlsx'
    // 使用钉钉API下载文件
    dd.saveFileToDingTalk({
      url: fileUrl,
      name: `${item.title}${new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: 'Asia/Shanghai'
      }).replace(/[\/\s,:]/g, '')}.xlsx`,
      success: (res) => {
        const { data = [] } = res
        if (!data.length) return
        showSuccessToast('导出成功')
      },
      fail: (err) => {
        // console.error('导出失败:', err)
        showFailToast('导出失败')
        dd.alert({
            content:JSON.stringify(err)
        })
      }
    })
  } catch (error) {
    closeToast()
    showFailToast('导出失败：' + (error.message || '未知错误'))
  }
}

// 防抖搜索函数
const debounceSearch = () => {
  // 清除之前的定时器
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }

  // 设置新的定时器，延迟500毫秒执行搜索
  searchTimeout.value = setTimeout(() => {
    // 只有当搜索值变化时才执行搜索
    formPage.value.pageNo = 1
    itemsList.value = []
    finished.value = false
    getItemsList()
  }, 500)
}

// 监听搜索值变化
watch(searchValue, () => {
  debounceSearch()
})

// 显示导入弹窗
const showImportDialog = () => {
  importText.value = ''
  importDialogVisible.value = true
}

// 处理导入确认
const handleImportConfirm = async () => {
  if (!importText.value.trim()) {
    showFailToast('请输入要导入的文本')
    return false // 阻止弹窗关闭
  }

  try {
    showToast({
      type: 'loading',
      message: '正在导入...',
      forbidClick: true,
      duration: 0
    })

    // 调用导入接口
    const res = await http.post('/api/dingapp/ecidenceYhytV2/importFromText', {
      importText: importText.value,
      userId
    })

    closeToast()

    if (res.success) {
      showSuccessToast('导入成功')
      // 重新加载列表
      formPage.value.pageNo = 1
      itemsList.value = []
      finished.value = false
      await getItemsList()
      return true // 允许弹窗关闭
    } else {
      showFailToast(res.msg || '导入失败')
      return false // 阻止弹窗关闭
    }
  } catch (error) {
    closeToast()
    showFailToast('导入失败：' + (error.data.msg || '未知错误'))
    return false // 阻止弹窗关闭
  }
}

onMounted(async () => {
  await getItemsList()
})
</script>

<style lang="scss" scoped>
.evidence-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  position: relative;
  padding-bottom: 60px;

  .search-panel {
    background-color: #FFFFFF;
    padding: 20px 7px 7px 7px;

    .search-box-wrapper {
      padding: 0;

      .search-box {
        display: flex;
        height: 40px;
        width: 100%;
        justify-content: space-between;
        align-items: center;
        gap: 0;

        .search-bar {
          flex: 1;
          padding: 0;
          background-color: #FFFFFF;
          width: 100%;

          :deep(.van-search__content) {
            border-radius: 20px;
            background-color: #f7f8fa;
          }

          :deep(.van-field__clear) {
            margin-right: 0;
          }
        }
      }
    }

    .add-btn-wrapper {
      margin-top: 12px;
      padding: 0 12px;
      margin-bottom: 12px;

      .btn-group {
        display: flex;
        gap: 10px;
      }

      .import-btn {
        border-radius: 8px;
        height: 44px;
        font-size: 16px;
        font-weight: 500;
        border: 1px solid #ebedf0;
        flex: 1;

        .van-button__icon {
          font-size: 18px;
          margin-right: 4px;
        }
      }

      .add-btn {
        border-radius: 8px;
        height: 44px;
        font-size: 16px;
        font-weight: 500;
        // background: linear-gradient(135deg, #1989fa, #0570db);
        // box-shadow: 0 4px 8px rgba(25, 137, 250, 0.2);
        border: none;
        flex: 2;

        .van-button__icon {
          font-size: 18px;
          margin-right: 4px;
        }

        // &:active {
        //   background: linear-gradient(135deg, #0570db, #0456a9);
        // }
      }
    }
  }

  .items-list {
    padding: 12px;
  }

  .swipe-cell {
    margin-bottom: 12px;
  }

  .delete-button {
    height: 100%;
    width: 65px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #ff4d4f;
    font-size: 14px;

    span {
      margin-top: 4px;
    }
  }

  .item-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;

    .item-actions {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      display: flex;
      flex-direction:row;
      justify-content: center;
      align-items: center;
      padding: 0 12px;
      z-index: 2;

      .action-icon {
        width: 36px;
        height: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        margin: 4px 0;

        &.delete-icon {
          &:active {
            background-color: rgba(0, 0, 0, 0.05);
          }
        }

        &.download-icon {
          background-color: rgba(25, 137, 250, 0.05);

          &:active {
            background-color: rgba(25, 137, 250, 0.1);
          }
        }
      }
    }

    .item-content {
      padding: 16px;
      padding-bottom: 16px;
      padding-right: 60px; /* 为右侧操作图标留出空间 */

      .item-title {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        margin-bottom: 8px;
      }

      .item-desc {
        font-size: 14px;
        color: #646566;
        margin-bottom: 8px;
      }

      .item-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #969799;

        .item-status {
          padding: 2px 6px;
          border-radius: 10px;
          font-size: 12px;

          &.pending {
            background-color: #ffe1e1;
            color: #ee0a24;
          }

          &.done {
            background-color: #e8f7e9;
            color: #07c160;
          }
        }
      }
    }
  }

  .add-button {
    position: fixed;
    right: 20px;
    bottom: 20px;
    z-index: 99;
  }
}

.exploration-dialog-content {
    padding: 16px;
}

.search-btn-container {
    margin-top: 16px;
}

/* 导入弹窗样式 */
.import-dialog {
    :deep(.van-dialog) {
        max-height: 80vh; /* 限制弹窗最大高度 */
        overflow: hidden; /* 防止内容溢出 */
        display: flex;
        flex-direction: column;
    }

    :deep(.van-dialog__content) {
        flex: 1;
        overflow: hidden; /* 防止内容溢出 */
    }
}

.import-dialog-content {
    padding: 16px;
    max-height: calc(60vh - 100px); /* 限制内容区域高度，留出空间给标题和按钮 */
    overflow-y: auto; /* 内容区域可滚动 */

    :deep(.van-field) {
        height: auto;
        margin-bottom: 0;
    }

    :deep(.van-field__control) {
        min-height: 120px;
        max-height: 40vh; /* 限制文本区域最大高度 */
        overflow-y: auto; /* 添加垂直滚动条 */
        resize: none; /* 移动端禁用resize */
        word-break: break-all; /* 确保长文本会换行 */
        white-space: pre-wrap; /* 保留空格和换行符 */
        line-height: 1.5;
        font-size: 14px;
    }
}
</style>