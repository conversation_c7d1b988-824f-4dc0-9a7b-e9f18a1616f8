import { http } from '@/utils/http'

/**
 * 获取勘查历史记录列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @returns {Promise<Object>} 勘查历史记录列表
 */
export function getSurveyHistoryList(params) {
  return http.get('/api/dingapp/exploration/history', { params })
}

/**
 * 获取勘查历史记录详情
 * @param {string} explorationId 勘查记录ID
 * @returns {Promise<Object>} 勘查历史记录详情
 */
export function getSurveyHistoryDetail(explorationId) {
  return http.get(`/api/dingapp/exploration/detail/${explorationId}`)
}

/**
 * 获取品规识别记录列表
 * @param {Object} params 查询参数
 * @param {string} params.explorationId 勘查记录ID
 * @param {number} params.current 当前页码
 * @param {number} params.size 每页大小
 * @returns {Promise<Object>} 品规识别记录列表
 */
export function getItemIdentifyCountList(params) {
  return http.get('/api/dingapp/itemIdentifyResults/countList', { params })
}
