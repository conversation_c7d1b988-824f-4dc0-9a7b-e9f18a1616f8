<template>
  <div class="evidence-detail">
    <!-- 顶部标题输入框和按钮 -->
    <div class="header">
      <div class="title-input">
        <van-field
          v-model="formData.title"
          placeholder="请输入标题"
          clearable
          type="textarea"
          rows="1"
          autosize
        />
      </div>

      <div class="header-buttons">
        <!-- 复制按钮 -->
        <van-button
          type="primary"
          icon="description"
          class="action-button"
          plain
          @click="copyFormattedText"
          title="复制模板"
        ></van-button>

        <!-- 添加按钮 -->
        <van-button
          type="primary"
          icon="plus"
          class="action-button"
          @click="toSearch"
          title="添加物品"
        ></van-button>
      </div>
    </div>

    <!-- 表单字段（折叠面板） -->
    <div class="form-fields">
      <van-collapse v-model="activeCollapse">
        <van-collapse-item title="案件信息" name="caseInfo">
          <!-- 执法机构 -->
          <van-field
            v-model="formData.enforcementAgency"
            label="执法机构"
            placeholder="请选择执法机构"
            readonly
            @click="showEnforcementAgencyPicker = true"
          />

          <!-- 案发时间 -->
          <van-field
            v-model="formData.caseTime"
            label="案发时间"
            placeholder="请选择案发时间"
            readonly
            @click="showCaseTimePicker = true"
          />

          <!-- 地址 -->
          <van-field
            v-model="formData.address"
            label="地址"
            placeholder="请输入地址"
            clearable
          />

          <!-- 详细地址 -->
          <!-- <van-field
            v-model="formData.detailedAddress"
            label="详细地址"
            placeholder="请输入详细地址"
            clearable
            type="textarea"
            rows="2"
            autosize
          /> -->

          <!-- 联合执法单位 -->
          <van-field
            v-model="formData.jointEnforcementAgency"
            label="联合执法单位"
            placeholder="请输入联合执法单位"
            clearable
          />

          <!-- 案由 -->
          <van-field
            v-model="formData.caseReason"
            label="案由"
            placeholder="请输入案由"
            clearable
            type="textarea"
            rows="2"
            autosize
          />

          <!-- 当事人 -->
          <van-field
            v-model="formData.partyInvolved"
            label="当事人"
            placeholder="请输入当事人"
            clearable
          />

          <!-- 许可证号 -->
          <van-field
            v-model="formData.licenseNo"
            label="许可证号"
            placeholder="请输入许可证号"
            clearable
          />
        </van-collapse-item>
      </van-collapse>
    </div>

    <!-- 执法机构选择弹出层 -->
    <van-action-sheet
      v-model:show="showEnforcementAgencyPicker"
      title="请选择执法机构"
      close-on-click-overlay
      cancel-text="取消"
      :actions="enforcementAgencyList"
      @select="handleEnforcementAgencySelect"
      @cancel="showEnforcementAgencyPicker = false"
    />

    <!-- 卷烟类型选择弹出层 -->
    <van-action-sheet
      v-model:show="showEvidenceTypePicker"
      title="请选择卷烟类型"
      close-on-click-overlay
      cancel-text="取消"
      :actions="evidenceTypeList"
      @select="handleEvidenceTypeSelect"
      @cancel="showEvidenceTypePicker = false"
    />

    <!-- 案发时间选择弹出层 -->
    <van-popup
      v-model:show="showCaseTimePicker"
      position="bottom"
      round
      :style="{ height: '50%' }"
    >
      <div class="case-time-picker">
        <!-- <div class="picker-header">
          <div class="title">选择案发时间</div>
        </div> -->
        <div class="picker-tabs">
          <div
            class="tab-item"
            :class="{ active: caseTimeActiveTab === 'date' }"
            @click="caseTimeActiveTab = 'date'"
          >
            日期
          </div>
          <div
            class="tab-item"
            :class="{ active: caseTimeActiveTab === 'time' }"
            @click="caseTimeActiveTab = 'time'"
          >
            时间
          </div>
        </div>
        <div class="picker-content">
          <!-- 日期选择器 -->
          <van-date-picker
            v-if="caseTimeActiveTab === 'date'"
            v-model="selectedCaseDate"
            title="选择日期"
            :min-date="new Date(2020, 0, 1)"
            :max-date="new Date(2050, 12, 31)"
            @confirm="onCaseDateConfirm"
            @cancel="showCaseTimePicker = false"
          />

          <!-- 时间选择器 -->
          <van-time-picker
            v-if="caseTimeActiveTab === 'time'"
            v-model="selectedCaseTime"
            title="选择时间"
            @confirm="onCaseTimeConfirm"
            @cancel="showCaseTimePicker = false"
          />
        </div>
      </div>
    </van-popup>


    <!-- 物品列表 -->
    <div class="item-list">
      <van-empty v-if="items.length === 0" description="暂无物品" />
      <van-swipe-cell v-else v-for="(item, index) in items" :key="index">
        <van-cell-group inset>
          <van-cell>
            <template #title>
              <div class="item-card">
                <div class="item-header">
                  <div class="item-name" v-if="item.stdType == '品规'">{{ item.productName }}</div>
                  <div class="item-name editable" v-else>
                    <van-field
                      v-model="item.productName"
                      placeholder="请输入名称"
                      input-align="left"
                      :border="false"
                      @change="onProductNameChange(item)"
                    />
                  </div>
                </div>
                <div class="item-info">
                  <span class="price-display">{{ `${Number(item.price).toFixed(2)}${item.priceUnit}` }}</span>
                  <van-tag v-if="item.priceSource" class="price-source" plain type="primary">{{ `${item.priceSource}` }}</van-tag>
                  <!-- 卷烟类型展示和选择 -->
                  <van-tag type="primary" class="evidence-type" @click="showEvidenceTypeSelector(item)"> {{ item.evidenceType || '选择类型' }}</van-tag>
                </div>
              </div>
            </template>
            <template #extra>
            <div class="stepper-container">
              <!-- <van-icon name="cross" @click="onDelete(item)"  class="delete-icon" /> -->
              <van-stepper
                  v-model="item.quantity" :decimal-length="1"
                  min="0.1" @change="onQuantityChange(item)" />
            </div>
          </template>
          </van-cell>
        </van-cell-group>
        <template #right>
          <div class="delete-button" @click="handleDeleteItem(index)">
              <van-icon name="delete-o" size="20" />
              <!-- <span>删除</span> -->
            </div>
        </template>
      </van-swipe-cell>
    </div>

    <!-- 统计信息 -->
    <div class="statistics">
      <van-cell-group inset>
        <van-cell title="品规数" :value="items.length + '个'" />
        <van-cell title="总条数" :value="totalQuantity + '条'" />
        <van-cell title="总金额" :value="'¥' + totalAmount + '元'" />
      </van-cell-group>
    </div>

    <!-- 底部操作区 -->
    <div class="footer">
      <van-button type="primary" block @click="handleSubmit">保存</van-button>
    </div>

    <!-- 模板选择弹窗 -->
    <van-dialog
      v-model:show="showTemplateDialog"
      title="选择复制模板"
      :show-confirm-button="false"
      close-on-click-overlay
      class="template-dialog"
      width="80%"
      :style="{ maxWidth: '500px' }"
    >
      <div class="template-list-container">
        <div class="template-list">
          <!-- 默认导出按钮 -->
          <van-button
            type="primary"
            block
            class="template-button default-template"
            @click="copyDefaultText(); showTemplateDialog = false;"
          >
            默认格式
          </van-button>

          <!-- 分隔线 -->
          <div class="template-divider">其他模板</div>

          <!-- 其他模板按钮 -->
          <van-button
            v-for="template in templateList"
            :key="template.id"
            type="primary"
            block
            class="template-button"
            @click="copyWithTemplate(template.template); showTemplateDialog = false;"
          >
            {{ template.name }}
          </van-button>
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { showSuccessToast, showFailToast, showToast, closeToast } from 'vant'
import { useRoute, useRouter } from 'vue-router'
import { useEvidenceItemsStore } from '@/stores/evidence-items'
import { http } from '@/utils/http'
import * as dd from 'dingtalk-jsapi'


// 物品列表
const items = ref([])

// 折叠面板状态控制
const activeCollapse = ref(['caseInfo']) // 默认展开案件信息面板

// 卷烟类型选择相关
const showEvidenceTypePicker = ref(false)
const currentEditingItem = ref(null)
const evidenceTypeList = ref([
  { name: '假烟', value: '假烟' },
  { name: '走私烟', value: '走私烟' },
  { name: '真烟', value: '真烟' }
])

// 案发时间选择相关
const showCaseTimePicker = ref(false)
const caseTimeActiveTab = ref('date')
const selectedCaseTime = ref(['00', '00'])
const tempCaseDate = ref('')
const tempCaseTime = ref('')

// 表单数据
const formData = ref({
  title: '',                     // 标题
  enforcementAgency: '',         // 执法机构
  caseTime: '',                  // 案发时间
  address: '',                   // 地址
  detailedAddress: '',           // 详细地址
  jointEnforcementAgency: '',    // 联合执法单位
  caseReason: '',                // 案由
  partyInvolved: '',             // 当事人
  licenseNo: ''                  // 许可证号
})

// 执法单位相关
const showEnforcementAgencyPicker = ref(false)
const enforcementAgencyList = ref([
  { name: '城区专卖管理中心', value: '城区专卖管理中心' },
  { name: '雷州市烟草专卖局', value: '雷州市烟草专卖局' },
  { name: '遂溪县烟草专卖局', value: '遂溪县烟草专卖局' },
  { name: '徐闻县草专卖局', value: '徐闻县草专卖局' },
  { name: '吴川市烟草专卖局', value: '吴川市烟草专卖局' },
  { name: '廉江市烟草专卖局', value: '廉江市烟草专卖局' }
])

// 处理执法单位选择
const handleEnforcementAgencySelect = (item) => {
  formData.value.enforcementAgency = item.name
  showEnforcementAgencyPicker.value = false
}

const dateToArray = (date) => {
  const year = String(date.getFullYear())
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return [year, month, day]
}

const selectedCaseDate = ref(dateToArray(new Date()))


// 格式化日期为 YYYY-MM-DD 格式
const formatDate = (dateArray) => {
  // 如果是数组格式 ['2021', '01', '01']
  if (Array.isArray(dateArray)) {
    const [year, month, day] = dateArray
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`
  }
  // 如果是Date对象
  else if (dateArray instanceof Date) {
    const year = dateArray.getFullYear()
    const month = String(dateArray.getMonth() + 1).padStart(2, '0')
    const day = String(dateArray.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }
  // 如果是其他格式，返回空字符串
  return ''
}

// 格式化时间为 HH:MM 格式
const formatTime = (timeArray) => {
  return timeArray.join(':')
}

// 处理日期选择确认
const onCaseDateConfirm = (value) => {
  // value 是Date对象
  tempCaseDate.value = formatDate(value)
  caseTimeActiveTab.value = 'time' // 自动切换到时间选择
}

// 处理时间选择确认
const onCaseTimeConfirm = (value) => {
  tempCaseTime.value = formatTime(value.selectedValues)
  confirmCaseTime() // 自动确认整个时间选择
}

// 确认案发时间选择
const confirmCaseTime = () => {
  // 如果日期未选择，使用当前选中的日期
  if (!tempCaseDate.value) {
    tempCaseDate.value = formatDate(selectedCaseDate.value)
  }

  // 如果时间未选择，使用当前选中的时间
  if (!tempCaseTime.value) {
    tempCaseTime.value = formatTime(selectedCaseTime.value)
  }

  // 保存原始格式的日期和时间（用于内部处理）
  const originalDateTime = `${tempCaseDate.value} ${tempCaseTime.value}`

  // 解析日期和时间，转换为"年月日时分"格式
  try {
    const [year, month, day] = tempCaseDate.value.split('-')
    const [hour, minute] = tempCaseTime.value.split(':')
    // 设置为"年月日时分"格式
    formData.value.caseTime = `${year}年${parseInt(month)}月${parseInt(day)}日${parseInt(hour)}时${parseInt(minute)}分`
  } catch (error) {
    console.error('格式化案发时间失败:', error)
    // 如果格式化失败，使用原始格式
    formData.value.caseTime = originalDateTime
  }

  // 关闭弹窗并重置临时变量
  showCaseTimePicker.value = false
  tempCaseDate.value = ''
  tempCaseTime.value = ''
}

// 获取路由参数和store
const route = useRoute()
const router = useRouter()
const evidenceItemsStore = useEvidenceItemsStore()
const licenseId = route.query.yhytId || ''
const selectionTime = route.query.selectionTime || ''
const isNewItem = route.query.id === 'new'

// 跳转到搜索页面
const toSearch = () => {
  // 保存当前列表数据和表单数据到 store
  evidenceItemsStore.setItems(items.value)
  // 保存所有表单字段
  evidenceItemsStore.setFormData(formData.value)
  router.push('/evidence-items/search')
}

// 从接口加载数据
const loadDataFromApi = async () => {
  // 如果没有 selectionTime，说明是新建，不需要加载数据
  if (!selectionTime) {
    console.log('没有 selectionTime，说明是新建，不需要加载数据')
    items.value = []
    return
  }

  try {
    const res = await http.get(`/api/dingapp/ecidenceYhyt/detailList?selectionTime=${selectionTime}`)
    if (res.data) {
      // 如果有数据，设置表单字段
      if (res.data[0]) {
        const data = res.data[0]
        // 处理案发时间格式
        let formattedCaseTime = data.caseTime || '';

        // 如果案发时间不是"年月日时分"格式，则转换
        if (formattedCaseTime && !formattedCaseTime.includes('年')) {
          try {
            const [datePart, timePart] = formattedCaseTime.split(' ');
            if (datePart && timePart) {
              const [year, month, day] = datePart.split('-');
              const [hour, minute] = timePart.split(':');
              formattedCaseTime = `${year}年${parseInt(month)}月${parseInt(day)}日${parseInt(hour)}时${parseInt(minute || 0)}分`;
            }
          } catch (error) {
            console.error('格式化案发时间失败:', error);
          }
        }

        // 设置表单数据
        formData.value = {
          title: data.title || '',
          enforcementAgency: data.enforcementAgency || '',
          caseTime: formattedCaseTime,
          address: data.address || '',
          detailedAddress: data.detailedAddress || '',
          jointEnforcementAgency: data.jointEnforcementAgency || '',
          caseReason: data.caseReason || '',
          partyInvolved: data.partyInvolved || '',
          licenseNo: data.licenseNo || ''
        }
      }

      // 处理物品数据
      const itemsData = res.data.items || res.data // 兼容两种数据结构
      const data = itemsData.map(item=>({
        id: item.priceStandardsId,
        price: item.currentUnitPrice,
        priceUnit: item.priceUnit,
        quantity: item.selectedQuantity,
        stdType: item.stdType,
        priceSource: item.priceSource,
        productName: item.productName,
        evidenceType: item.evidenceType || (item.stdType === '品规' ? '假烟' : ''), // 使用后端返回的evidenceType，如果没有则根据stdType设置默认值
        packageQty2: (item.packageQty2 > 0) ? item.packageQty2 : 200 // 如果packageQty2不是正数，默认使用200
      }))
      items.value = data
    } else {
      showFailToast(res.msg || '加载数据失败')
    }
  } catch (error) {
    showFailToast('加载数据失败：' + (error.message || '未知错误'))
  }
}

// 处理数据加载
onMounted(async () => {
  // 检查 store 中是否有数据
  const storeItems = evidenceItemsStore.items
  const storeFormData = evidenceItemsStore.formData

  if (storeItems && storeItems.length > 0) {
    // 如果 store 中有数据，使用 store 中的数据
    items.value = storeItems

    // 恢复所有表单字段
    if (storeFormData) {
      formData.value = {
        title: storeFormData.title || '',
        enforcementAgency: storeFormData.enforcementAgency || '',
        caseTime: storeFormData.caseTime || '',
        address: storeFormData.address || '',
        detailedAddress: storeFormData.detailedAddress || '',
        jointEnforcementAgency: storeFormData.jointEnforcementAgency || '',
        caseReason: storeFormData.caseReason || '',
        partyInvolved: storeFormData.partyInvolved || '',
        licenseNo: storeFormData.licenseNo || ''
      }
    }

    // 使用完后清空 store 数据
    evidenceItemsStore.clearItems()
    evidenceItemsStore.clearFormData()
  } else {
    // 如果 store 中没有数据，从接口加载
    evidenceItemsStore.clearItems()
    evidenceItemsStore.clearFormData()

    await loadDataFromApi()
  }

  // 加载模板数据
  getTemplateDictData()

  // 初始化日期和时间选择器的默认值
  const now = new Date()
  selectedCaseDate.value = dateToArray(now)
  selectedCaseTime.value = [
    String(now.getHours()).padStart(2, '0'),
    String(now.getMinutes()).padStart(2, '0')
  ]

  // 如果已有案发时间，解析并设置到选择器中
  if (formData.value.caseTime) {
    try {
      // 检查是否已经是"年月日时分"格式
      const yearMatch = formData.value.caseTime.match(/(\d{4})年(\d{1,2})月(\d{1,2})日(\d{1,2})时(\d{1,2})分/)

      if (yearMatch) {
        // 如果是"年月日时分"格式，提取年月日时分
        const [_, year, month, day, hour, minute] = yearMatch
        // 注意：月份需要减1，因为Date对象的月份是从0开始的
        selectedCaseDate.value = dateToArray(new Date(Number(year), Number(month) - 1, Number(day)))
        selectedCaseTime.value = [
          String(hour).padStart(2, '0'),
          String(minute).padStart(2, '0')
        ]
      } else {
        // 如果是旧格式（YYYY-MM-DD HH:MM），按原来的方式解析
        const [datePart, timePart] = formData.value.caseTime.split(' ')
        if (datePart) {
          const [year, month, day] = datePart.split('-').map(Number)
          if (year && month && day) {
            // 注意：月份需要减1，因为Date对象的月份是从0开始的
            selectedCaseDate.value = dateToArray(new Date(year, month - 1, day))
          }
        }

        if (timePart) {
          const [hours, minutes] = timePart.split(':')
          if (hours && minutes) {
            selectedCaseTime.value = [
              hours.padStart(2, '0'),
              minutes.padStart(2, '0')
            ]
          }
        }

        // 转换为"年月日时分"格式
        if (datePart && timePart) {
          const [year, month, day] = datePart.split('-')
          const [hour, minute] = timePart.split(':')
          formData.value.caseTime = `${year}年${parseInt(month)}月${parseInt(day)}日${parseInt(hour)}时${parseInt(minute || 0)}分`
        }
      }
    } catch (error) {
      console.error('解析案发时间失败:', error)
    }
  }
})



// 组件卸载时清除选中的物品
onUnmounted(() => {
  const currentRoute = router.currentRoute.value
  if (currentRoute.path !== '/evidence-items/search') {
    // 如果不是跳转到搜索页面，清除所有数据
    evidenceItemsStore.clearItems()
    evidenceItemsStore.clearFormData()
  }


})

// 计算总数量和总金额
const totalQuantity = computed(() => {
  // 使用更精确的方法计算总数量，避免浮点数精度问题
  const total = items.value.reduce((sum, item) => {
    // 将字符串转换为数字，并确保至少保留1位小数
    const quantity = Number(parseFloat(item.quantity).toFixed(1));
    // 使用加法计算，避免直接相加可能带来的精度问题
    return (sum * 10 + quantity * 10) / 10;
  }, 0);

  // 最终结果四舍五入到1位小数
  return Math.round(total * 10) / 10;
})

const totalAmount = computed(() => {
  // 使用更精确的计算方法，避免浮点数精度问题
  const sum = items.value.reduce((sum, item) => {
    // 将价格和数量转换为字符串，然后使用乘法计算，避免浮点数精度问题
    const price = parseFloat(item.price);
    const quantity = parseFloat(item.quantity);

    // 将价格和数量乘以1000，转为整数计算，避免浮点数精度问题
    const priceInt = Math.round(price * 1000);
    const quantityInt = Math.round(quantity * 1000);

    // 计算总价（整数），然后除以1000000得到正确的小数
    const itemTotal = (priceInt * quantityInt) / 1000000;

    return sum + itemTotal;
  }, 0);

  // 使用Math.floor确保不会进行四舍五入，然后除以1000并保留3位小数
  const roundedSum = Math.floor(sum * 1000) / 1000;

  // 格式化为字符串，确保显示3位小数（即使末尾是0）
  return roundedSum.toFixed(3);
});

// 删除物品
const handleDeleteItem = (index) => {
  items.value.splice(index, 1)
  showToast('已删除')
}

const onDelete = (item) => {
  console.log(`点击删除图标，删除商品: ${item.productName}`);
  // 实现你的删除逻辑
  const index = items.value.findIndex(i => i.id === item.id);
  if (index !== -1) {
    items.value.splice(index, 1);
    showToast('已删除');
  }
};

// 提交处理
const handleSubmit = async () => {
  if (items.value.length === 0) {
    showFailToast('请添加物品')
    return
  }

  // 验证表单必填字段
  if (!formData.value.title.trim()) {
    showFailToast('请输入标题')
    return
  }

  if (!formData.value.enforcementAgency) {
    showFailToast('请选择执法机构')
    return
  }

  // 准备提交数据
  const submitData = {
    // 表单字段 - 直接使用整个formData对象
    ...formData.value,

    // 物品列表
    priceStandardsList: items.value.map(item => ({
      productName: item.productName,
      currentUnitPrice: Number(item.price),
      selectedQuantity: item.quantity,
      priceStandardsId: item.id,
      stdType: item.stdType,
      priceUnit: item.priceUnit,
      priceSource: item.priceSource,
      evidenceType: item.evidenceType || '',
      packageQty2: item.packageQty2, // 保存每条卷烟的支数
      selectionTime: selectionTime??'',
    }))
  }

  try {
    // 提交整个对象
    const res = await http.post('/api/dingapp/ecidenceYhyt/submit', submitData)
    if (res.success) {
      showSuccessToast('提交成功')

      router.back()
    } else {
      showFailToast(res.msg || '提交失败')
    }
  } catch (error) {
    showFailToast('提交失败：' + (error.message || '未知错误'))
  }
}

const onQuantityChange = (item) => {
  console.log(`商品 ${item.productName} 的数量变为: ${item.quantity}`);
  // 在这里可以执行更新总价、发送请求等操作
  // showToast(`商品 ${item.productName} 数量已更新为 ${item.quantity}`);
};

const onProductNameChange = (item) => {
  console.log(`商品名称已修改为: ${item.productName}`);
  // 如果需要，可以在这里添加额外的逻辑，比如验证或者发送请求
  // showToast(`商品名称已更新为 ${item.productName}`);
};

// 显示卷烟类型选择器
const showEvidenceTypeSelector = (item) => {
  currentEditingItem.value = item;
  showEvidenceTypePicker.value = true;
};

// 处理卷烟类型选择
const handleEvidenceTypeSelect = (type) => {
  if (currentEditingItem.value) {
    currentEditingItem.value.evidenceType = type.value;
    showEvidenceTypePicker.value = false;
    currentEditingItem.value = null;
  }
};

// 复制功能相关变量

// 模板相关数据
const templateList = ref([]);
const showTemplateDialog = ref(false);

// 获取模板字典数据
const getTemplateDictData = async () => {
  try {
    // 如果模板列表已经有数据，不重复加载
    if (templateList.value.length > 0) {
      return;
    }

    // 清空模板列表
    templateList.value = [];

    // 获取电子烟案件快报模板
    const electronicRes = await http.get('/api/dingapp/dict-biz/list?dictValue=电子烟案件快报')
    if (electronicRes.data && electronicRes.data.length > 0) {
      const electronicTemplates = await http.get('/api/dingapp/dict-biz/child-list?parentId=' + electronicRes.data[0].parentId)
      if (electronicTemplates.data && electronicTemplates.data.length > 0) {
        // 添加电子烟案件快报模板
        const templates = electronicTemplates.data.map(item => ({
          id: item.id,
          name: item.dictValue,
          template: item.dictKey
        }));
        // 直接赋值，不使用累加操作
        templateList.value = templates;
      }
    }

  } catch (error) {
    console.error('获取模板数据失败:', error);
    showFailToast('获取模板数据失败: ' + (error.message || '未知错误'));
  }
}

// 显示模板选择弹窗
const copyFormattedText = async () => {
  // 如果还没有加载模板，则加载模板
  if (templateList.value.length === 0) {
      // 等待模板加载完成
      await getTemplateDictData();
  }

  // 显示模板选择弹窗，即使没有其他模板，也至少有默认模板
  showTemplateDialog.value = true;
}

// 格式化数据为指定文本格式
const formatItemsToText = () => {
  if (!items.value || items.value.length === 0) {
    return '暂无数据';
  }

  // 将物品按类型分组
  const cigarettes = items.value.filter(item => item.stdType === '品规');
  const otherItems = items.value.filter(item => item.stdType !== '品规');

  let resultText = '';

  // 处理卷烟（保持原有格式）
  if (cigarettes.length > 0) {
    // 使用原始数据，保持原始顺序（不排序）
    const originalCigarettes = cigarettes.map(item => ({
      name: item.productName,
      quantity: Number(item.quantity),
      price: Number(item.price),
      priceUnit: item.priceUnit
    }));

    // 格式化详细的卷烟数据（用于默认格式），保持原始顺序
    const formattedCigarettes = originalCigarettes.map(item => {
      return `${item.name}${item.quantity}条`;
    }).join('、');

    // 计算卷烟统计数据
    const cigarettesCount = originalCigarettes.length; // 不同品规的数量

    // 使用更精确的方法计算总数量
    let cigarettesTotalQuantity = cigarettes.reduce((sum, item) => sum + Number(item.quantity), 0);
    cigarettesTotalQuantity = Math.round(cigarettesTotalQuantity * 10) / 10;

    // 计算总支数 - 使用packageQty2值计算支数，如果没有则默认使用200
    const totalSticks = cigarettes.reduce((sum, item) => {
      const packageQty = item.packageQty2 ? Number(item.packageQty2) : 200;
      return sum + Math.round(Number(item.quantity) * packageQty);
    }, 0);

    // 计算卷烟总金额
    const cigarettesTotalAmount = cigarettes.reduce((sum, item) => {
      return sum + Number(item.price) * Number(item.quantity);
    }, 0);

    const cigarettesAmountInWan = (cigarettesTotalAmount / 10000).toFixed(4);

    // 返回详细的格式化文本
    const totalSticksWan = (totalSticks / 10000).toFixed(2); // 支数保留两位小数
    resultText = `卷烟：${formattedCigarettes}，卷烟合计共${cigarettesCount}个品种规格${cigarettesTotalQuantity}条(${totalSticksWan}万支)，总货值${cigarettesAmountInWan}万元。`;
  }

  // 处理其他物品
  if (otherItems.length > 0) {
    const formattedOtherItems = otherItems.map(item => {
      return `${item.productName}${item.quantity}${item.priceUnit}`;
    }).join('、');

    const otherItemsCount = otherItems.length;
    const otherItemsTotalAmount = otherItems.reduce((sum, item) => sum + Number(item.price) * Number(item.quantity), 0);
    const otherItemsAmountInWan = (otherItemsTotalAmount / 10000).toFixed(4);

    if (resultText) resultText += ' ';
    resultText += `其他物品：${formattedOtherItems}，合计共${otherItemsCount}个品种，总货值${otherItemsAmountInWan}万元。`;
  }

  return resultText || '暂无数据';
}

// 生成简化的物品描述，用于模板
const generateSimplifiedItemDescription = () => {
  if (!items.value || items.value.length === 0) {
    return '暂无数据';
  }

  // 获取前5个物品名称，用于简短描述
  const topItems = items.value.slice(0, 5).map(item => item.productName);
  const hasMoreItems = items.value.length > 5;

  // 格式化物品描述
  let itemsDescription = '';
  if (topItems.length > 0) {
    itemsDescription = topItems.join('、');
    if (hasMoreItems) {
      itemsDescription += '等';
    }
  }

  // 计算卷烟支数（如果有）
  const cigarettes = items.value.filter(item => item.stdType === '品规');
  let totalSticksWan = '';

  if (cigarettes.length > 0) {
    const totalSticks = cigarettes.reduce((sum, item) => {
      const packageQty = item.packageQty2 ? Number(item.packageQty2) : 200;
      return sum + Math.round(Number(item.quantity) * packageQty);
    }, 0);
    totalSticksWan = (totalSticks / 10000).toFixed(2) + '万支';
  }

  // 计算总金额（所有物品）
  const totalAmount = items.value.reduce((sum, item) => {
    return sum + Number(item.price) * Number(item.quantity);
  }, 0);

  const amountInWan = (totalAmount / 10000).toFixed(4);

  // 统计信息
  const totalCount = items.value.length;

  // 为模板格式化的简化文本
  let result = `涉及${itemsDescription}${totalCount}个品种`;
  if (totalSticksWan) {
    result += totalSticksWan;
  }
  result += `，案值约${amountInWan}万元。`;

  return result;
}

// 生成按卷烟类型分类的数据，用于广东湛江战报模板
const generateZhanjiangReportDescription = () => {
  if (!items.value || items.value.length === 0) {
    return {
      totalCount: 0,
      totalSticksWan: '0',
      amountInWan: '0',
      fakeCount: 0,
      fakeSticksWan: '0',
      smuggledCount: 0,
      smuggledSticksWan: '0',
      genuineCount: 0,
      genuineSticksWan: '0'
    };
  }

  // 将物品按类型分组
  const cigarettes = items.value.filter(item => item.stdType === '品规');

  // 计算所有物品的总案值
  const allItemsTotalAmount = items.value.reduce((sum, item) => sum + Number(item.price) * Number(item.quantity), 0);
  const allItemsAmountInWan = (allItemsTotalAmount / 10000).toFixed(4);

  if (cigarettes.length === 0) {
    return {
      totalCount: items.value.length,
      totalSticksWan: '0',
      amountInWan: allItemsAmountInWan,
      fakeCount: 0,
      fakeSticksWan: '0',
      smuggledCount: 0,
      smuggledSticksWan: '0',
      genuineCount: 0,
      genuineSticksWan: '0'
    };
  }

  // 按evidenceType分组
  const fakeCigarettes = cigarettes.filter(item => item.evidenceType === '假烟');
  const smuggledCigarettes = cigarettes.filter(item => item.evidenceType === '走私烟');
  const genuineCigarettes = cigarettes.filter(item => item.evidenceType === '真烟');

  // 计算总数据
  const totalCount = cigarettes.length;
  let totalQuantity = cigarettes.reduce((sum, item) => sum + Number(item.quantity), 0);
  totalQuantity = Math.round(totalQuantity * 10) / 10;

  // 计算总支数 - 使用packageQty2值计算支数，如果没有则默认使用200
  const totalSticks = cigarettes.reduce((sum, item) => {
    const packageQty = item.packageQty2 ? Number(item.packageQty2) : 200;
    return sum + Math.round(Number(item.quantity) * packageQty);
  }, 0);

  const totalSticksWan = (totalSticks / 10000).toFixed(2); // 支数保留两位小数
  const totalAmount = cigarettes.reduce((sum, item) => sum + Number(item.price) * Number(item.quantity), 0);
  const amountInWan = (totalAmount / 10000).toFixed(4); // 案值保留四位小数

  // 计算假烟数据
  const fakeCount = fakeCigarettes.length;
  let fakeQuantity = fakeCigarettes.reduce((sum, item) => sum + Number(item.quantity), 0);
  fakeQuantity = Math.round(fakeQuantity * 10) / 10;

  // 计算假烟支数 - 使用packageQty2值计算支数，如果没有则默认使用200
  const fakeSticks = fakeCigarettes.reduce((sum, item) => {
    const packageQty = item.packageQty2 ? Number(item.packageQty2) : 200;
    return sum + Math.round(Number(item.quantity) * packageQty);
  }, 0);

  const fakeSticksWan = (fakeSticks / 10000).toFixed(2); // 支数保留两位小数

  // 计算走私烟数据
  const smuggledCount = smuggledCigarettes.length;
  let smuggledQuantity = smuggledCigarettes.reduce((sum, item) => sum + Number(item.quantity), 0);
  smuggledQuantity = Math.round(smuggledQuantity * 10) / 10;

  // 计算走私烟支数 - 使用packageQty2值计算支数，如果没有则默认使用200
  const smuggledSticks = smuggledCigarettes.reduce((sum, item) => {
    const packageQty = item.packageQty2 ? Number(item.packageQty2) : 200;
    return sum + Math.round(Number(item.quantity) * packageQty);
  }, 0);

  const smuggledSticksWan = (smuggledSticks / 10000).toFixed(2); // 支数保留两位小数

  // 计算真烟数据
  const genuineCount = genuineCigarettes.length;
  let genuineQuantity = genuineCigarettes.reduce((sum, item) => sum + Number(item.quantity), 0);
  genuineQuantity = Math.round(genuineQuantity * 10) / 10;

  // 计算真烟支数 - 使用packageQty2值计算支数，如果没有则默认使用200
  const genuineSticks = genuineCigarettes.reduce((sum, item) => {
    const packageQty = item.packageQty2 ? Number(item.packageQty2) : 200;
    return sum + Math.round(Number(item.quantity) * packageQty);
  }, 0);

  const genuineSticksWan = (genuineSticks / 10000).toFixed(2); // 支数保留两位小数

  // 返回数据对象，不包含任何固定文本
  return {
    totalCount: items.value.length, // 包含所有物品的总数量
    totalSticksWan,
    amountInWan: allItemsAmountInWan, // 使用所有物品的总案值
    fakeCount,
    fakeSticksWan,
    smuggledCount,
    smuggledSticksWan,
    genuineCount,
    genuineSticksWan
  };
}

// 复制默认格式的文本
const copyDefaultText = async () => {
  // 显示加载提示
  showToast({
    type: 'loading',
    message: '正在生成文本...',
    forbidClick: true,
    duration: 0
  });

  try {
    const text = formatItemsToText();

    // 尝试使用DingTalk的复制API（如果在钉钉环境中）
    if (typeof dd !== 'undefined' && dd.biz && dd.biz.util && dd.biz.util.copyToClipboard) {
      dd.biz.util.copyToClipboard({
        text: text,
        onSuccess: function() {
          closeToast();
          showSuccessToast('文本已复制到剪贴板');
        },
        onFail: function() {
          // 如果钉钉API失败，尝试其他方法
          fallbackCopy(text);
        }
      });
    } else {
      // 如果不在钉钉环境中，尝试使用标准方法
      await standardCopy(text);
    }
  } catch (error) {
    console.error('复制文本失败:', error);
    closeToast();
    showFailToast('复制文本失败: ' + (error.message || '未知错误'));
  }
}

// 根据模板替换内容并复制
const copyWithTemplate = async (template) => {
  // 显示加载提示
  showToast({
    type: 'loading',
    message: '正在生成文本...',
    forbidClick: true,
    duration: 0
  });

  try {
    // 获取简化的物品描述，用于模板
    const simplifiedItemDescription = generateSimplifiedItemDescription();

    // 获取湛江战报格式的数据
    const zhanjiangData = generateZhanjiangReportDescription();

    // 准备替换数据
    const replacementData = {
      // 物品描述（简化版）
      '{itemDescription}': simplifiedItemDescription,

      // 湛江战报格式的数据字段
      '{totalCount}': zhanjiangData.totalCount.toString(),
      '{totalSticksWan}': zhanjiangData.totalSticksWan,
      '{amountInWan}': zhanjiangData.amountInWan,
      '{fakeCount}': zhanjiangData.fakeCount.toString(),
      '{fakeSticksWan}': zhanjiangData.fakeSticksWan,
      '{smuggledCount}': zhanjiangData.smuggledCount.toString(),
      '{smuggledSticksWan}': zhanjiangData.smuggledSticksWan,
      '{genuineCount}': zhanjiangData.genuineCount.toString(),
      '{genuineSticksWan}': zhanjiangData.genuineSticksWan,
      // 案值（万元）- 保留4位小数
      '{caseValue}': (Number(totalAmount.value) / 10000).toFixed(4),
      // 品规数
      '{itemCount}': items.value.length.toString(),
      // 总条数
      '{totalQuantity}': totalQuantity.value.toString(),
      // 总金额
      '{totalAmount}': totalAmount.value,
      // 标题
      '{title}': formData.value.title,
      // 案件相关信息
      '{enforcementAgency}': formData.value.enforcementAgency,
      '{caseTime}': formData.value.caseTime,
      '{address}': formData.value.address,
      '{detailedAddress}': formData.value.detailedAddress,
      '{jointEnforcementAgency}': formData.value.jointEnforcementAgency,
      '{caseReason}': formData.value.caseReason,
      '{partyInvolved}': formData.value.partyInvolved,
      '{licenseNo}': formData.value.licenseNo,

    };

    // 替换模板中的占位符
    let finalText = template;
    for (const [placeholder, value] of Object.entries(replacementData)) {
      finalText = finalText.replace(new RegExp(placeholder, 'g'), value || '');
    }

    // 尝试使用DingTalk的复制API（如果在钉钉环境中）
    if (typeof dd !== 'undefined' && dd.biz && dd.biz.util && dd.biz.util.copyToClipboard) {
      dd.biz.util.copyToClipboard({
        text: finalText,
        onSuccess: function() {
          closeToast();
          showSuccessToast('文本已复制到剪贴板');
        },
        onFail: function() {
          // 如果钉钉API失败，尝试其他方法
          fallbackCopy(finalText);
        }
      });
    } else {
      // 如果不在钉钉环境中，尝试使用标准方法
      await standardCopy(finalText);
    }
  } catch (error) {
    console.error('复制文本失败:', error);
    closeToast();
    showFailToast('复制文本失败: ' + (error.message || '未知错误'));
  }
}

// 标准复制方法
const standardCopy = async (text) => {
  try {
    // 使用Clipboard API复制文本
    await navigator.clipboard.writeText(text);
    closeToast();
    showSuccessToast('文本已复制到剪贴板');
  } catch (error) {
    console.error('Clipboard API失败:', error);
    // 如果Clipboard API失败，尝试使用传统方法
    fallbackCopy(text);
  }
}

// 后备复制方法
const fallbackCopy = (text) => {
  try {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    // 设置样式使其不可见但可选择
    textarea.style.position = 'fixed';
    textarea.style.opacity = '0';
    textarea.style.top = '0';
    textarea.style.left = '0';

    document.body.appendChild(textarea);

    // 在iOS上，需要特殊处理
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
      const range = document.createRange();
      range.selectNodeContents(textarea);
      const selection = window.getSelection();
      selection.removeAllRanges();
      selection.addRange(range);
      textarea.setSelectionRange(0, 999999);
    } else {
      textarea.select();
    }

    const successful = document.execCommand('copy');
    document.body.removeChild(textarea);

    if (successful) {
      closeToast();
      showSuccessToast('文本已复制到剪贴板');
    } else {
      throw new Error('execCommand返回false');
    }
  } catch (fallbackError) {
    console.error('备用复制方法也失败:', fallbackError);
    closeToast();
    showFailToast('复制文本失败，请手动复制');
  }
}

const downExcel = async () => {
  try {
    // 显示加载提示
    // showToast({
    //   type: 'loading',
    //   message: '正在获取文件...',
    //   forbidClick: true,
    //   duration: 0
    // })


    // 调用接口获取文件URL
    const res = await http.get(`/api/dingapp/ecidenceYhyt/exportExcel?yhytId=${licenseId}&selectionTime=${selectionTime}`)
    console.log(res.data)
    // 关闭加载提示
    closeToast();

    if (!res.success || !res.data) {
      showFailToast(res.msg || '获取文件地址失败')
      return
    }

    // 从接口返回中获取文件URL
    const minioBaseUrl = import.meta.env.VITE_BASE_MINIO_URI

    // 获取文件路径
    let path = res.data.path || ''

    // 如果是开发环境，需要处理/minio前缀
    if (import.meta.env.MODE === 'development' && path.startsWith('/minio')) {
      path = path.replace('/minio', '')
    }

    // 构建完整的文件URL
    const fileUrl =  `${minioBaseUrl}${path}`
    // console.log('构建的文件URL:', fileUrl)
    // showToast(fileUrl)


    // 使用钉钉API下载文件
    dd.saveFileToDingTalk({
      url:fileUrl,
      // url: 'https://frp-act.com:63756/images/upload/20250417/16f121e27b9f566361581f71ff32d2e8.xlsx',
      // header: { 'content-type': 'application/vnd.openxmlformats-officedocument.spreadsheetm.sheet' },
      name: `涉案物品清单${new Date().toLocaleDateString().replace(/\//g, '')}.xlsx`,
      success: (res) => {
        // const { filePath } = res;
        // showToast(filePath)
        const { data = [] } = res;
        if(!data.length) return
        const {
          fileId = "",
          fileName = "",
          fileSize = 0,
          fileType = "",
          spaceId = "" } = data[0]
          // 保存成功调起预览  需要鉴权
        // dd.previewFileInDingTalk({
        //   spaceId: spaceId,
        //   fileName: fileName,
        //   fileSize: fileSize,
        //   fileSize: fileType,
        //   fileId: fileId,
        //   success: () => {
        //     // console.log('预览成功===', data)
        //   },
        //   fail: (err1) => {
        //     showToast(err1.errorMessage)
        //   },
        //   complete: () => {
        //     // console.log('结束===')
        //   },
        // });
      },
      fail: (err) => {
        showToast(err.errorMessage)
      },
      complete: () => {},
    });

  } catch (error) {
    // 关闭加载提示
    // console.error('下载过程出错:', error)
    showFailToast('下载失败: ' + (error.message || '未知错误'))
  }
}

const downExcel1 = async()=>{
  dd.downloadFile({
    url: 'http://**************:9000/images/upload/20250417/16f121e27b9f566361581f71ff32d2e8.xlsx', // 替换为您的文件下载链接
  success: function(res) {
    const { filePath } = res;
    showToast()
    dd.openDocument({
      filePath: filePath,
      fileType: 'xlsx',
      success: () => {},
      fail: () => {},
      complete: () => {},
    });
  },
  fail: function(err) {
    console.log('下载文件失败', err);
  }
});
}

</script>

<style scoped lang="scss">
.evidence-detail {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f7f8fa;
}

.header {
  padding: 12px 16px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between; /* 使标题输入框和按钮分开对齐 */
  align-items: flex-start; /* 改为顶部对齐，以便标题换行时布局正常 */
  min-height: 60px; /* 设置最小高度，确保按钮位置稳定 */
}

.header-buttons {
  display: flex;
  align-items: flex-start; /* 按钮顶部对齐 */
  margin-top: 4px; /* 稍微下移按钮，与标题第一行文字对齐 */
}

.action-button {
  flex-shrink: 0;
  height: 36px;
  width: 36px;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  margin-left: 8px;
}

.title-input {
  flex: 1;
  margin-right: 12px;
  min-width: 0; /* 确保flex子项可以正确收缩 */
  max-width: 70%; /* 限制最大宽度，确保按钮有足够空间 */
}

.title-input :deep(.van-field__control) {
  font-size: 16px;
  font-weight: 500;
  word-break: break-word; /* 允许在单词内换行 */
  white-space: normal; /* 允许文本换行 */
  line-height: 1.3; /* 设置合适的行高 */
}

.form-fields {
  padding: 0;
  background-color: #fff;
  margin-bottom: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 折叠面板样式 */
:deep(.van-collapse) {
  width: 100%;
}

:deep(.van-collapse-item__title) {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  padding: 12px 16px;
}

:deep(.van-collapse-item__content) {
  padding: 0;
}

:deep(.van-field) {
  padding: 10px 16px;
}

.item-list {
  flex: 1;
  /* padding: 12px; */
}

.item-card {
  background: #fff;
  border-radius: 8px;
  /* padding: 12px; */
  margin-bottom: 6px;
  /* box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); */
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.item-name {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.evidence-type {
  padding: 0px 8px;
  // background-color: #f2f3f5;
  // color: #1989fa;
  border-radius: 4px;
  // font-size: 14px;
  margin-left: 0; /* 移除左边距，因为我们已经在item-info中添加了gap */
  border: 1px solid #e8e8e8;
  max-width: 100%; /* 确保标签不会超出容器 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-name.editable {
  border-bottom: 1px dashed #ddd;
}

.item-name.editable :deep(.van-field__control) {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding: 0;
}

.item-name.editable :deep(.van-field) {
  padding: 0;
}

.item-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap; /* 允许内容换行 */
  color: #666;
  font-size: 14px;
  gap: 5px; /* 添加元素间距 */
}

.price-display {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
  margin-right: 5px;
}

.price-source {
  margin-left: 0; /* 移除左边距，因为我们已经在item-info中添加了gap */
  // color: #666;
  // font-size: 14px;
  max-width: 100%; /* 确保标签不会超出容器 */
}

.statistics {
  background: #fff;
  /* padding: 16px; */
  margin: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.footer {
  padding: 16px;
  background: #fff;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.1);
}

.license-select {
  margin-bottom: 12px;
}

.form-header,
.quantity-controls {
  display: none;
}
:deep(.van-cell-group--inset) {
  margin: 12px;
}

:deep(.delete-button ) {
    height: 100%;
    width: 45px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #ff4d4f;
    font-size: 14px;

    span {
      margin-top: 4px;
    }
  }

.stepper-container {
  display: flex;
  flex-direction: column; /* 将删除图标和步进器垂直排列 */
  justify-content: end;
}

.delete-icon {
  margin-bottom: 16px; /* 调整删除图标和步进器之间的间距 */
  cursor: pointer; /* 添加点击手势 */
}

/* 模板弹窗样式 */
:deep(.template-dialog) {
  .van-dialog__content {
    max-height: 60vh;
    overflow: hidden;
  }
}

.template-list-container {
  max-height: 50vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding-bottom: 20px; /* 添加底部内边距，确保最后一个按钮完全显示 */
}

.template-list {
  padding: 16px;

  .template-button {
    margin-bottom: 16px; /* 增加按钮间距 */
    height: auto; /* 允许按钮高度自适应 */
    padding: 10px 15px; /* 增加按钮内边距 */
    white-space: normal; /* 允许文本换行 */
    line-height: 1.5; /* 增加行高 */

    &:last-child {
      margin-bottom: 20px; /* 确保最后一个按钮有足够的底部间距 */
    }
  }

  .default-template {
    background-color: #1989fa;
    font-weight: bold;
  }

  .template-divider {
    position: relative;
    margin: 16px 0;
    text-align: center;
    color: #969799;
    font-size: 14px;

    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 30%;
      height: 1px;
      background-color: #ebedf0;
    }

    &::before {
      left: 0;
    }

    &::after {
      right: 0;
    }
  }
}

/* 案发时间选择器样式 */
.case-time-picker {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.picker-header .title {
  font-size: 16px;
  font-weight: bold;
}

.picker-header .cancel-btn,
.picker-header .confirm-btn {
  padding: 4px 8px;
  color: #1989fa;
}

.picker-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.picker-tabs .tab-item {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  font-size: 14px;
  color: #666;
}

.picker-tabs .tab-item.active {
  color: #1989fa;
  position: relative;
}

.picker-tabs .tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 2px;
  background-color: #1989fa;
}

.picker-content {
  flex: 1;
  overflow: hidden;
}
</style>